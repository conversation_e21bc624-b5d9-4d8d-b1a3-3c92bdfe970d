<?php
define('LEAD_SOURCES', [
    'friend_recommendation' => '50000316043',
    'facebook_ads' => '50000316048',
    'group_shares' => '50000316038',
    'google_website' => '50000316037',
    'planning_poker' => '50000316048',
    'tiktok_ads' => '50000931279',
    'linkedin_ads' => '50000931278',
    'other' => '50000316048'
]);
define('CRM_API_BASE_URL', 'https://crm.scrumpass.com/crm/sales');
define('CRM_API_AUTH_TOKEN', '_1IWPs2JvZIyXgDAg2RsoA');
define('CRM_OWNER_ID', '50000112237'); // ScrumPass Support
define('CRM_SUBSCRIPTION_STATUS_NEW', '50000204566');
define('CRM_SUBSCRIPTION_STATUS_IN_PROGRESS', '50000204573');

$telegram_token = '**********************************************';

$chat_id = '-1002364170421';
if (defined('ENVIRONMENT') && in_array(ENVIRONMENT, ['production', 'testing'])) {
    if ($site_id == 0) {
        $telegram_token = '**********************************************';
		$chat_id = '-1002384156547';
    } elseif ($site_id == 2) {
		$chat_id = '-*************';
        $telegram_token = '**********************************************';
    }
}

define('TELEGRAM_BOT_TOKEN', $telegram_token);
define('TELEGRAM_CHAT_ID', $chat_id);


defined('BASEPATH') or exit('No direct script access allowed');
class ApiSignUp extends CI_Controller
{
	function __construct()
	{
		parent::__construct();
		$this->load->database();
		$this->load->helper('url');
		$this->load->model("user_model");
		$this->load->model("result_model");
		$this->load->model("account_model");
		$this->load->model("qbank_model");
		$this->load->model("quiz_model");
		$this->load->model("payment_model");
		$this->load->model("language_model");
		$this->load->helper('cookie');
		$this->lang->load(
			'basic',
			$this->language_model->get()
		);
	}

	public function log_api($type, $text = "")
	{
		$current_date = date("d-m-Y");
		$filename = "application/api_logs/logs-" . $current_date . ".txt";
		$myfile = fopen($filename, "a+");
		if ($type == 1) { // type = 1: Request - 2: Response
			$txt = "=> Request: " . json_encode($_POST) . "\n";
		} else {
			$txt = "=> Response: " . $text . "\n";
		}
		fwrite($myfile, $txt);
		fclose($myfile);
	}

	public function check_email_duplicate()
	{
		$email = $this->input->post('email');
		if (empty($email)) {
			$response = ['error' => true, 'message' => 'Email là bắt buộc'];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
			return;
		}

		$exists = $this->user_model->check_email_by_email($email);

		if ($exists) {
			$response = ['error' => true, 'message' => 'Email đã tồn tại'];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		} else {
			$response = ['error' => false, 'message' => 'Email chưa tồn tại'];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		}
	}

	public function add_account()
	{
		$this->log_api(1);

		$email = $this->input->post('email');
		$email_check = $this->user_model->check_email_by_email($email);

		if ($email_check) {
			return;
		}

		$postData = $this->input->post();

		$selected_premium = $this->input->post('product_packet');
		$category_id = $this->input->post('category_id');
		$payment_status = $this->input->post('payment_status');

		$this->db->where('cid', $category_id);
		$query = $this->db->get('savsoft_category');

		if ($query !== false && $query->num_rows() > 0) {
			$category = $query->row();
			$category_name = $category->category_name;
		} else {
			$category_name = 'Unknown';
		}

		try {
			$user = $this->user_model->insert_account();

			$user['product_packet'] = $selected_premium;
			$user['category_name'] = $category_name;
			$user['payment_status'] = $payment_status;

			$response = ['error' => false, 'data' => $user['uid'], 'message' => 'Account created successfully.'];
			$this->send_telegram_message($user);
			$this->create_sales_contact($postData);

			// $this->create_deal($postData['certification'], $postData['mentor']);
		} catch (\Throwable $th) {
			$response = ['error' => true, 'message' => 'Account creation failed.'];
		}

		$this->log_api(2, json_encode($response));
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}

	private function send_telegram_message($user, $content = null)
	{
		$url = "https://api.telegram.org/bot" . TELEGRAM_BOT_TOKEN . "/sendMessage";

		if ($content !== null) {
			$message = $content;
		} else {
			$message = "📢 Có user đăng ký mới!\n"
				. "Email: " . $user['email'] . "\n"
				. "Tên: " . $user['name'] . "\n"
				. "ĐT: " . $user['contact_no'] . "\n"
				. "Tài khoản mạng xã hội: " . $user['social_media'] . "\n"
				. "Công ty: " . $user['company'] . "\n"
				. "Chức danh: " . $user['job_title'] . "\n"
				. "Nguồn: " . $user['know_from'] . "\n"
				. "Người giới thiệu: " . $user['introducer'] . "\n"
				. "Ngày thi: " . $user['exam_date'] . "\n"
				. "Địa chỉ nhận bưu phẩm: " . $user['address'] . "\n"
				. "Gói sản phẩm: " . ($user['product_packet'] ? 'Premium' : 'Lite') . "\n"
				. "Tên chứng chỉ: " . $user['category_name'] . "\n"
				. "Trạng thái thanh toán: " . ($user['payment_status'] ? "Thanh toán ngay" : "Thanh toán sau") . "\n"
				. "From site: " . base_url() . "\n";
		}
		$post_fields = [
			'chat_id' => TELEGRAM_CHAT_ID,
			'text' => $message,
			'parse_mode' => 'HTML'
		];

		try {
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

			// This line for private IPs
			// curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			$result = curl_exec($ch);
			curl_close($ch);
		} catch (\Throwable $th) {
			$result = null;
		}

		return $result;
	}

	private function get_lead_source_id($source) {
		return isset(LEAD_SOURCES[$source]) ? LEAD_SOURCES[$source] : null;
	}

	private function create_sales_contact($postData)
	{
		$owner_id = CRM_OWNER_ID;
		$subscription_status = CRM_SUBSCRIPTION_STATUS_IN_PROGRESS;
		$lead_source_id = $this->get_lead_source_id($postData['know_from']); // Ánh xạ lead_source_id mới

		$data = array(
			'first_name' => $postData['name'],
			'email' => $postData['email'],
			'mobile_number' => $postData['contact_no'],
			'facebook' => $postData['social_media'],
			'owner_id' => $owner_id,
			'subscription_status' => $subscription_status,
			'job_title' => $postData['job_title'],
			'company' => $postData['company'],
			'address' => $postData['address'],
			'lead_source_id' => $lead_source_id,
			'created_at' => date("Y-m-d H:i:s")
		);

		$crm_api_url = CRM_API_BASE_URL . '/api/contacts';
		$headers = [
			'Authorization: Token token=' . CRM_API_AUTH_TOKEN
		];

		$response = $this->cUrl($crm_api_url, $data, true, $headers);

		$response_data = json_decode($response, true);
		if (isset($response_data['error'])) {
			throw new \Exception('Error creating Sales Account');
		}

		$this->log_api(2, $response);
		return $response_data;
	}


	public function update_payment_status()
	{
		$input_data = $this->input->post();

		$uid = isset($input_data['uid']) ? $input_data['uid'] : null;
		$payment_status = isset($input_data['payment_status']) ? $input_data['payment_status'] : null;

		if (empty($uid) || $payment_status === null) {
			$response = ['error' => true, 'message' => 'Can not update payment status.'];
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($response));
			return;
		}

		$uid = (int)$uid;
		$payment_status = (int)$payment_status;

		$data = [
			'uid' => $uid,
			'payment_status' => $payment_status,
			'payment_gateway' => $input_data['payment_method'],
			'product_packet' => $input_data['product_packet'], // gói lite hay full
		];

		$result = $this->user_model->update_payment_status($data);
		if ($payment_status == 1) {
			$this->user_model->update_status_user($uid, 'Active');
		}
		if ($result) {
			$response = ['error' => false, 'message' => 'Success'];
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($response));
		} else {
			$response = ['error' => true, 'message' => 'Can not update payment status.'];
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($response));
		}
	}

	private function cUrl($url, $data = null, $is_post = false, $headers = [])
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		if ($is_post) {
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		}
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
			'Content-Type: application/json'
		], $headers));
		curl_setopt($ch, CURLOPT_CAINFO, __DIR__ . '/path/to/cacert.pem');
		curl_setopt($ch, CURLOPT_VERBOSE, true);
		curl_setopt($ch, CURLOPT_STDERR, fopen('php://stderr', 'w'));
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

		$result = curl_exec($ch);
		$error = curl_error($ch);
		curl_close($ch);

		if ($error) {
			return json_encode(['error' => 'CURL Error: ' . $error]);
		}

		return $result;
	}

	private function create_deal($certification, $mentor)
	{
		$deal_api_url = 'https://crm.scrumpass.com/crm/sales/api/selector/deals';
		$headers = [
			'Authorization: Token token=_1IWPs2JvZIyXgDAg2RsoA',
			'Content-Type: application/json'
		];

		$data = array(
			'name' => 'Exam Tool ' . $certification,
			// 'amount' => $this->get_product_price($certification),
			// 'product_id' => $this->get_product_id($certification),
			// 'deal_stage_id' => $this->get_deal_stage_id('Won'),
			'certification' => $certification,
			'mentor' => $mentor
		);

		$curl = curl_init($deal_api_url);
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec($curl);
		$error = curl_error($curl);
		curl_close($curl);

		if ($error) {
			return array('error' => 'CURL Error: ' . $error);
		}

		$response_data = json_decode($response, true);
		if (isset($response_data['error'])) {
			return array('error' => 'Lỗi khi tạo deal: ' . $response_data['error']);
		}

		return $response_data;
	}

	private function get_mentor_id($cid)
	{
		switch ($cid) {
			case 3:
			// case 16:
				return 1434; // Nguyễn Tuấn Anh
			case 1:
			case 4:
			case 5:
				return 298; // Nguyễn Thị Hồng Việt
			case 17:
			case 7:
			case 6:
				return 3180; // Din Chí Thành
			case 16:
			case 19:
				return 728; // Vũ Tuấn Anh
			case 2:
				return 2120; // Phạm Hồng Thắng
			default:
				return null; // Không tìm thấy mentor phù hợp
		}
	}

	public function getFirstLogin()
	{
		$this->log_api(1);

		$requestData = json_decode(file_get_contents('php://input'), true);
		$uid = $requestData['uid'] ?? $this->input->get('uid', TRUE);

		if (!$uid) {
			$response = ['error' => true, 'message' => 'User not logged in or UID missing'];
			$this->log_api(2, json_encode($response));
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($response));
			return;
		}

		$this->db->select('last_login_date');
		$this->db->where('uid', $uid);
		$query = $this->db->get('savsoft_users');
		$result = $query->row_array();

		if ($result) {
			$response = ['error' => false, 'last_login_date' => $result['last_login_date']];
		} else {
			$response = ['error' => true, 'message' => 'User not found'];
		}

		$this->log_api(2, json_encode($response));
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}

	public function get_upgrade_price()
	{
		$uid = $this->input->post('uid');

		$result = $this->user_model->get_upgrade_price($uid);

		if ($result) {
			$response = ['error' => false, 'result' => $result];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		} else {
			$response = ['error' => true, 'message' => 'User not found'];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		}
	}

	public function upgrade_account()
	{
		$uid = $this->input->post('uid');
		$plan = $this->input->post('product_packet');
		$certId = $this->input->post('category_id');
		$paymentStatus = $this->input->post('payment_status');
		$paymentMethod = $this->input->post('payment_method');

		$data = [
			'uid' => $uid,
			'product_packet' => $plan,
			'category_id' => $certId,
			'payment_status' => $paymentStatus,
			'payment_method' => $paymentMethod
		];

		$cert = $this->qbank_model->find_cid($certId);
		$user = $this->user_model->get_user_by_uid($uid);

		// $result = $this->user_model->upgrade_account($data);
		$message = "📢 Có user đăng ký nâng cấp tài khoản!\n"
				. "ID: " . $uid . "\n"
				. "Email: " . $user['email'] . "\n"
				. "Tên: " . $user['name'] . "\n"
				. "ĐT: " . $user['contact_no'] . "\n"
				// . "Tài khoản mạng xã hội: " . $user['social_media'] . "\n"
				// . "Công ty: " . $user['company'] . "\n"
				// . "Chức danh: " . $user['job_title'] . "\n"
				// . "Nguồn: " . $user['know_from'] . "\n"
				// . "Người giới thiệu: " . $user['introducer'] . "\n"
				// . "Ngày thi: " . $user['exam_date'] . "\n"
				// . "Địa chỉ nhận bưu phẩm: " . $user['address'] . "\n"
				. "Gói sản phẩm: " . ($plan == 'full' ? 'Premium' : 'Lite') . "\n"
				. "Tên chứng chỉ: " . $cert['category_name'] . "\n"
				. "Trạng thái thanh toán: " . ($paymentStatus == 'immediate' ? "Thanh toán ngay" : "Thanh toán sau") . "\n"
				. "From site: " . base_url() . "\n";
		$this->send_telegram_message([], $message);

		// if ($result) {
		// 	$response = ['error' => false, 'result' => $result];
		// 	$this->output
		// 	->set_content_type('application/json')
		// 	->set_output(json_encode($response));
		// } else {
		// 	$response = ['error' => true, 'message' => 'User not found'];
		// 	$this->output
		// 	->set_content_type('application/json')
		// 	->set_output(json_encode($response));
		// }
		$response = ['error' => false, 'result' => true];
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}

	public function get_user_cert()
	{
		$uid = $this->input->post('uid');
		$result = $this->user_model->get_user_cert($uid);
		if ($result) {
			$response = ['error' => false, 'result' => $result];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		} else {
			$response = ['error' => true, 'message' => 'User not found'];
			$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
		}
	}

	public function add_more_cert()
	{
		$uid = $this->input->post('uid');
		$plan = $this->input->post('product_packet');
		$certId = $this->input->post('category_id');
		$paymentStatus = $this->input->post('payment_status');
		$paymentMethod = $this->input->post('payment_method');

		$data = [
			'uid' => $uid,
			'product_packet' => $plan,
			'category_id' => $certId,
			'payment_status' => $paymentStatus,
			'payment_method' => $paymentMethod
		];

		$cert = $this->qbank_model->find_cid($certId);
		$user = $this->user_model->get_user_by_uid($uid);

		$message = "📢 Có user đăng ký thêm chứng chỉ!\n"
				. "ID: " . $uid . "\n"
				. "Email: " . $user['email'] . "\n"
				. "Tên: " . $user['name'] . "\n"
				. "ĐT: " . $user['contact_no'] . "\n"
				// . "Tài khoản mạng xã hội: " . $user['social_media'] . "\n"
				// . "Công ty: " . $user['company'] . "\n"
				// . "Chức danh: " . $user['job_title'] . "\n"
				// . "Nguồn: " . $user['know_from'] . "\n"
				// . "Người giới thiệu: " . $user['introducer'] . "\n"
				// . "Ngày thi: " . $user['exam_date'] . "\n"
				// . "Địa chỉ nhận bưu phẩm: " . $user['address'] . "\n"
				. "Gói sản phẩm: " . ($plan == 'full' ? 'Premium' : 'Lite') . "\n"
				. "Tên chứng chỉ: " . $cert['category_name'] . "\n"
				. "Trạng thái thanh toán: " . ($paymentStatus == 'immediate' ? "Thanh toán ngay" : "Thanh toán sau") . "\n"
				. "From site: " . base_url() . "\n";
		$this->send_telegram_message([], $message);
		$response = ['error' => false, 'result' => true];
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}

	public function purchase_success()
	{
		$id = $this->input->get('id');
		$data['id'] = $id;
		$this->load->view('pages/purchase_success', $data);
	}

	public function process_purchase()
	{
		$email = $this->input->post('email');
		$name = $this->input->post('name');
		$cert = $this->input->post('cert');
		$plan = $this->input->post('plan');
		$trans = $this->input->post('trans_id');
		$knowFrom = $this->input->post('know_from');
		$examDate = $this->input->post('exam_date');
		$password = $this->input->post('password');

		$user = $this->user_model->get_user_by_email($email);
		$is_new_user = count($user) == 0;

		if ($is_new_user) {
			// create user
			$user = $this->user_model->insert_account_itcertprep([
				'email' => $email,
				'name' => $name,
				'cert' => $cert,
				'plan' => $plan,
				'trans_id' => $trans,
				'know_from' => $knowFrom,
				'exam_date' => $examDate,
				'password' => $password
			]);

			// Send welcome email for new users
			$this->send_welcome_email($email, $name, $cert, $plan);

		} else {
			// add cert
			$this->user_model->add_cert_itcertprep([
				'uid' => $user['uid'],
				'cert' => $cert,
				'plan' => $plan,
				'trans_id' => $trans
			]);
		}
		$response = ['error' => false, 'result' => true];
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}

	/**
	 * Send welcome email to new itcertprep users
	 *
	 * @param string $email User's email
	 * @param string $name User's name
	 * @param int $cert_id Certification ID
	 * @param string $plan Subscription plan
	 * @return bool Whether the email was sent successfully
	 */
	private function send_welcome_email($email, $name, $cert_id, $plan)
	{
		// Get certification name
		$category = $this->db->select('category_name')->where('cid', $cert_id)->get('savsoft_category')->row_array();
		$cert_name = $category ? $category['category_name'] : 'Unknown Certification';

		// Format plan name
		$plan_name = ($plan == '1month_option') ? '1 Month' : '12 Months';

		// Load email library
		$this->load->library('email');

		// Configure email settings
		if ($this->config->item('protocol') == "smtp") {
			$config['protocol'] = 'smtp';
			$config['smtp_host'] = $this->config->item('smtp_hostname');
			$config['smtp_user'] = $this->config->item('smtp_username');
			$config['smtp_pass'] = $this->config->item('smtp_password');
			$config['smtp_port'] = $this->config->item('smtp_port');
			$config['smtp_timeout'] = $this->config->item('smtp_timeout');
			$config['mailtype'] = 'html';
			// $config['starttls'] = $this->config->item('starttls');
			$config['newline'] = $this->config->item('newline');
			// $config['smtp_crypto'] = $this->config->item('smtp_crypto');
			$this->email->initialize($config);
		}

		// Load email template
		$message = $this->load->view('email_templates/itcertprep_welcome', [], true);

		// Replace placeholders with actual data
		$message = str_replace('[name]', $name, $message);
		$message = str_replace('[email]', $email, $message);
		// $message = str_replace('[certification]', $cert_name, $message);
		// $message = str_replace('[plan]', $plan_name, $message);
		// $message = str_replace('[login_url]', site_url('login'), $message);
		// $message = str_replace('[current_year]', date('Y'), $message);

		// Set email parameters
		$fromemail = $this->config->item('fromemail');
		$fromname = $this->config->item('fromname');
		$subject = "Welcome to the ITCertPrep learning community!";

		$this->email->to($email);
		$this->email->from($fromemail, $fromname);
		$this->email->subject($subject);
		$this->email->message($message);

		// Send email
		$result = $this->email->send();

		// Log email sending attempt
		if (!$result) {
			// Log detailed error information when email fails
			$error_log = "Email sending failed to $email. Error details: " . $this->email->print_debugger(false);
			$this->log_api(2, $error_log);

			// You can also log to a specific email error log file
			$current_date = date("d-m-Y");
			$filename = "application/api_logs/email_errors-" . $current_date . ".txt";
			$myfile = fopen($filename, "a+");
			$txt = date("Y-m-d H:i:s") . " - Failed to send email to: $email - " . $this->email->print_debugger(false) . "\n";
			fwrite($myfile, $txt);
			fclose($myfile);
		} else {
			$this->log_api(2, "Welcome email to $email: Success");
		}

		return $result;
	}
}